FROM cr.ttyuyin.com/public/python:3.12

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

WORKDIR /usr/app

# 安装 pip & poetry（并使用国内源）
RUN pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip install poetry -i https://pypi.tuna.tsinghua.edu.cn/simple

# 配置 poetry 使用国内源
RUN poetry config repositories.tuna https://pypi.tuna.tsinghua.edu.cn/simple \
    && poetry config installer.max-workers 4 \
    && poetry config virtualenvs.create false \
    && poetry config cache-dir /tmp/poetry-cache

# 添加代码（最后添加避免层缓存无效）
ADD . .

# 安装依赖
RUN poetry install --no-interaction --no-ansi --no-root --with=dev

# 入口命令
CMD ["poetry", "run", "python", "-u", "main.py"]
