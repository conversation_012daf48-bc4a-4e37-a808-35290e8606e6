import os
from functools import cache
from typing import Type, Tuple, Dict, Optional, Any

from pydantic import BaseModel, Field
from pydantic_settings import (
    BaseSettings,
    EnvSettingsSource,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
)

from src.infra.config import TomlConfigByEnvSettingsSource
from src.infra.utils import get_work_dir
from src.schema import const


@cache
def get_or_create_settings_ins() -> "GlobalConfig":
    return GlobalConfig()


class BaseConfig(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_nested_delimiter="__", extra="ignore")

    @classmethod
    def settings_customise_sources(
            cls,
            settings_cls: Type[BaseSettings],
            init_settings: PydanticBaseSettingsSource,
            env_settings: PydanticBaseSettingsSource,
            dotenv_settings: PydanticBaseSettingsSource,
            file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:
        config_dir = os.path.join(get_work_dir(), "config")
        return (
            init_settings,
            EnvSettingsSource(settings_cls),
            dotenv_settings,  # 添加.env文件支持
            TomlConfigByEnvSettingsSource(settings_cls, config_dir),
        )


class AppSettings(BaseModel):
    name: str = Field(default=const.APP_EN_NAME, description="The name of the setting")
    host: str = Field(default="0.0.0.0", description="The host of the setting")
    log_level: str = Field(default="DEBUG", description="The log level of the setting")
    thread_pool_size: int = Field(
        default=50, description="The thread pool size of the setting"
    )
    http_port: int = Field(default=8888, description="The http port of the setting")
    http_openapi_url: str = Field(
        default="/openapi.json", description="The http openapi url of the setting"
    )
    http_docs_url: str = Field(
        default="/docs", description="The http docs url of the setting"
    )


class MysqlSettings(BaseModel):
    host: str = Field(..., description="MySQL服务器主机地址")
    port: int = Field(..., description="MySQL服务器端口")
    user: str = Field(..., description="MySQL用户名")
    password: str = Field(..., description="MySQL密码")
    db: str = Field(default="guanfu", description="MySQL数据库名")
    charset: str = Field(default="utf8mb4", description="MySQL字符集")
    conn_timeout: int = Field(default=10, description="连接超时时间(秒)")
    debug: bool = Field(default=False, description="是否开启SQL调试模式")

    # 连接池配置
    pool_size: int = Field(default=8, description="连接池基础大小 (适合1核1G MySQL)")
    max_overflow: int = Field(default=50, description="允许的额外连接数")
    pool_recycle: int = Field(default=1800, description="连接回收时间(秒)")
    pool_timeout: int = Field(default=30, description="获取连接的超时时间(秒)")
    pool_pre_ping: bool = Field(default=True, description="是否在使用前检查连接有效性")


class LLMSettings(BaseModel):
    provider: str = Field(description="The type of LLM")
    model: str = Field(description="The name of the LLM")
    temperature: float = Field(description="The temperature of the LLM", default=0.0)
    max_tokens: int = Field(description="The max tokens of the LLM", default=8196)
    external_args: Optional[Dict[str, Any]] = Field(default=None, description="The external arguments of the LLM")
    extra_body: Optional[Dict[str, Any]] = Field(default=None, description="The extra body of the LLM")


class ModelSettings(BaseModel):
    default: str = Field(default="gpt41", description="默认模型")
    coordinate: str = Field(default="ui_tars", description="坐标模型")
    action_verification: str = Field(default="gpt4o", description="动作验证模型")


class PathSettings(BaseModel):
    """路径配置"""
    # 截图相关路径
    screenshot_base_dir: str = Field(
        default="reports/ui_agent_test/reports",
        description="截图存储基础目录"
    )

    # 视频相关路径
    video_base_dir: str = Field(
        default="reports/ui_agent_test/video",
        description="视频存储基础目录"
    )
    video_temp_dir: str = Field(
        default="reports/ui_agent_test/video/temp",
        description="视频流式上传临时目录"
    )

    # 视频服务配置
    video_max_upload_size: int = Field(
        default=50 * 1024 * 1024,  # 50MB
        description="最大上传文件大小（字节）"
    )
    video_session_timeout: int = Field(
        default=600,  # 10分钟
        description="会话超时时间（秒）"
    )
    video_hmac_secret: str = Field(
        default="video_upload_secret_key_2025",
        description="HMAC密钥（生产环境应使用环境变量）"
    )

    # 豆包AI配置
    doubao_api_key: str = Field(
        default="",
        description="豆包API密钥"
    )
    doubao_base_url: str = Field(
        default="https://ark.cn-beijing.volces.com/api/v3",
        description="豆包API基础URL"
    )
    doubao_model: str = Field(
        default="doubao-seed-1-6-250615",
        description="豆包模型名称"
    )


class GlobalConfig(BaseConfig):
    app: AppSettings = Field(default_factory=AppSettings)
    mysql: MysqlSettings = Field(default_factory=MysqlSettings)
    models: ModelSettings = Field(default_factory=ModelSettings)
    paths: PathSettings = Field(default_factory=PathSettings)
    llms: Dict[str, LLMSettings] = Field(..., description="LLM配置")


if __name__ == "__main__":
    print(GlobalConfig().app)
