# ClickPilot

🤖 **智能UI自动化测试Agent** - 点击领航员，引导自动化测试

## 📋 项目简介

ClickPilot 是一个基于 Python 的智能UI自动化测试系统，通过AI
Agent技术实现智能化的用户界面测试。系统由决策Agent、执行Agent和监督Agent三部分组成，能够自动分析APP功能、制定测试策略、执行测试操作并监控测试过程。

**项目名称构成**: Click（点击） + Pilot（领航员）

## 🎯 核心组件

### 1. 决策Agent (DecisionAgent)

- 🧠 **深度界面分析**: 基于截图进行全面的界面状态分析，理解所有UI元素和组件
- 📋 **智能策略制定**: 结合测试目标和执行历史，制定最优的下一步执行策略
- 🔄 **执行历史回顾**: 分析之前动作的执行效果，避免重复无效操作和死循环
- 🎯 **动作决策**: 生成具体的动作命令（不含坐标），支持10种动作类型
- ✅ **自检机制**: 内置重复动作检测和动作有效性验证，确保执行质量

### 2. 执行Agent (ExecutionAgent)

- 🎯 **坐标补充**: 使用专门的坐标模型为需要坐标的动作精确定位
- 🎮 **动作执行**: 通过ADB命令执行具体的UI操作（点击、滑动、输入等）
- 📱 **设备交互**: 与Android设备进行实时交互，处理各种UI组件
- ⏱️ **性能监控**: 记录每个动作的执行时间和结果状态
- 📸 **截图管理**: 统一管理执行前后的截图，支持相对路径和绝对路径

### 3. 监督Agent (SupervisorAgent)

- 🔍 **设备状态监控**: 实时检查Android设备连接状态和响应能力
- 📱 **应用状态管理**: 确保目标应用已安装并在前台运行，支持自动启动
- 🚨 **执行限制监控**: 基于步骤数量动态设置执行限制，防止无限循环
- 🤖 **AI错误检测**: 使用大模型分析执行历史，智能识别执行错误和死循环
- 🔄 **自动恢复**: 支持应用切换和状态恢复，确保测试环境稳定

## ✨ 主要特性

- 🤖 **多Agent协作**: 决策、执行、监督三个Agent协同工作，实现智能化测试
- 🎯 **精准坐标定位**: 专门的坐标模型确保UI操作的高精度执行
- 📊 **实时状态监控**: 全程监控设备连接、应用状态和执行进度
- 🔄 **智能错误恢复**: AI驱动的错误检测和自动状态恢复机制
- 📸 **统一截图管理**: 优化的截图时序管理，支持执行前后对比分析
- 🧠 **自适应策略**: 基于执行历史的动态策略调整，避免重复无效操作
- 📝 **完整执行日志**: 详细记录每个动作的决策过程、执行结果和耗时
- 🔀 **双验证模式**: 支持分步验证和聚合验证两种测试模式
- 🌐 **RESTful API**: 基于FastAPI的现代化测试服务接口
- 📅 **任务调度系统**: 支持定时任务和批量测试执行
- 🔔 **飞书集成**: 实时推送测试结果和异常报警
- 🛡️ **任务停止控制**: 支持任务的实时停止和状态管理
- 🎬 **AI视频分析**: 基于豆包AI的视频内容分析和测试用例自动生成
- 📤 **视频流式上传**: 支持大文件视频的流式上传和自动管理
- ⌨️ **智能键盘管理**: 自动化ADB键盘设置和输入法切换
- 🖼️ **统一图像处理**: 集成图像标注、压缩、格式转换等功能
- 🔧 **Prompt参数化**: 支持自定义应用描述和UI操作说明
- 🐳 **容器化部署**: 支持Docker容器化部署和运行
- 🧹 **自动清理机制**: 定时清理过期视频文件和临时数据

## 🏗️ 系统架构

```
├── src/
│   ├── api/                     # API接口层
│   │   └── v1/                  # API版本1
│   │       ├── dto/             # 数据传输对象
│   │       └── ui_task.py       # UI任务API
│   │
│   ├── application/             # 应用服务层
│   │   ├── ui_task/             # UI任务应用服务
│   │   └── ui_task_application.py # UI任务应用服务实现
│   │
│   ├── domain/                  # 领域模型层
│   │   └── ui_task/             # UI任务领域模型
│   │       └── mobile/          # 移动端UI任务
│   │           ├── aggregate/   # 聚合器
│   │           │   ├── agent/   # Agent实现
│   │           │   │   ├── decision_agent.py            # 决策Agent
│   │           │   │   ├── execution_agent.py           # 执行Agent
│   │           │   │   ├── vserification_agent.py       # 验证Agent
│   │           │   │   └── supervisor_agent.py          # 监督Agent
│   │           │   └── agent_aggregate.py               # Agent聚合器
│   │           │
│   │           ├── android/     # Android工具
│   │           │   ├── action_tool.py                   # 动作工具
│   │           │   ├── image_processor.py               # 统一图像处理器（合并图像标注和处理功能）
│   │           │   ├── keyboard_manager.py              # ADB键盘管理器
│   │           │   └── screenshot_manager.py            # 截图管理器
│   │           │
│   │           ├── repo/        # 仓储层
│   │           │   ├── do/      # 数据对象
│   │           │   │   ├── State.py                     # 状态对象
│   │           │   │   └── task_stop_manager.py         # 任务停止管理器
│   │           │   ├── dao.py                           # 数据访问对象
│   │           │   └── ui_task_repository.py            # UI任务仓储
│   │           │
│   │           ├── service/     # 服务层
│   │           │   ├── keyboard_service.py              # 键盘服务
│   │           │   ├── step_executor_service.py         # 步骤执行服务
│   │           │   ├── task_persistence_service.py      # 任务持久化服务
│   │           │   ├── test_case_parser_service.py      # 测试用例解析服务
│   │           │   └── video_service.py                 # 视频分析服务
│   │           │
│   │
│   ├── infra/                   # 基础设施层
│   │   ├── app/                 # 应用核心
│   │   ├── clients/             # 外部客户端
│   │   │   ├── lark/            # 飞书机器人集成
│   │   │   └── mysql/           # MySQL数据库操作
│   │   ├── config/              # 配置管理
│   │   ├── events/              # 事件总线
│   │   ├── log/                 # 日志系统
│   │   ├── model/               # 模型管理
│   │   ├── scheduler/           # 任务调度器
│   │   └── web/                 # Web服务框架
│   │
│   ├── scheduler/               # 测试调度模块
│   │   └── video_cleanup.py     # 视频清理定时任务
│   └── schema/                  # 数据模型定义
│       ├── action_types.py      # 动作类型定义
│       ├── const.py             # 常量定义
│       ├── constants.py         # 系统常量
│       └── env.py               # 环境变量
│
├── alembic/                     # 数据库迁移
│   └── versions/                # 迁移版本
├── config/                      # 配置文件目录
├── screenshots/                 # 截图存储目录
├── reports/                     # 测试报告和视频存储目录
│   └── ui_agent_test/           # UI测试相关文件
│       └── video/               # 视频文件存储（按日期分类）
├── Dockerfile                   # Docker容器化配置
└── .kiro/                       # Kiro配置目录
    ├── specs/                   # 规格说明
    └── steering/                # 引导规则

```

## 🔄 工作流程

```mermaid
graph TD
    A[开始测试] --> A1[解析测试用例]
    A1 --> A2[初始化状态]
    A2 --> B[执行步骤节点]
    B --> B1{任务已完成?}
    B1 -->|是| END[测试结束]
    B1 -->|否| B2{任务被停止?}
    B2 -->|是| STOP[任务停止]
    B2 -->|否| B3[监督检查]
    B3 --> B4[检查设备连接]
    B4 --> B5[检查应用状态]
    B5 --> B6[检查执行限制]
    B6 --> B7{监督检查通过?}
    B7 -->|否| FAIL[任务失败]
    B7 -->|是| C[统一截图流程]
    C --> C1[初始化执行轮次]
    C1 --> C2[拍摄当前界面截图]
    C2 --> C3[转换截图为Base64]
    C3 --> C4{截图成功?}
    C4 -->|否| ERROR[处理截图错误]
    C4 -->|是| D[决策Agent分析]
    D --> D1[分析界面状态]
    D1 --> D2[回顾执行历史]
    D2 --> D3[制定执行策略]
    D3 --> D4[生成动作命令]
    D4 --> D5{是否finished动作?}
    D5 -->|是| COMPLETE[标记任务完成]
    D5 -->|否| E[执行Agent处理]
    E --> E1{需要坐标?}
    E1 -->|是| E2[坐标模型补充坐标]
    E1 -->|否| E3[直接执行动作]
    E2 --> E3
    E3 --> E4[执行具体操作]
    E4 --> F[拍摄执行后截图]
    F --> G[处理验证结果]
    G --> G1{步骤验证模式?}
    G1 -->|分步验证| G2[执行步骤验证]
    G1 -->|聚合验证| G3[记录执行历史]
    G2 --> G4{验证通过?}
    G4 -->|是| G5[移动到下一步]
    G4 -->|否| G6[步骤重试]
    G3 --> H[更新状态]
    G5 --> H
    G6 --> H
    H --> H1{所有步骤完成?}
    H1 -->|是| COMPLETE
    H1 -->|否| B
    ERROR --> END
    FAIL --> END
    STOP --> END
    COMPLETE --> END
```

### 🔍 详细工作流程说明

#### 1. 任务初始化阶段

- **测试用例解析**: 根据验证模式（分步/聚合）解析测试用例
    - 分步模式：使用预定义的步骤列表
    - 聚合模式：使用GPT-4智能拆分任务为独立步骤
- **状态初始化**: 创建DeploymentState，设置任务参数和执行环境

#### 2. 监督检查阶段

- **设备连接检查**: 验证Android设备是否连接并可响应命令
- **应用状态检查**: 确认目标应用已安装且在前台运行
- **执行限制检查**: 监控动作执行次数，防止死循环（默认步骤数×5倍限制）
- **智能错误检测**: 使用AI模型分析执行历史，检测是否陷入错误循环

#### 3. 决策分析阶段

- **界面状态分析**: 深度分析当前截图，理解界面元素和状态
- **执行历史回顾**: 分析之前的动作效果，避免重复无效操作
- **策略制定**: 基于当前状态和目标，制定下一步执行策略
- **动作生成**: 输出具体的动作命令（如click、scroll、type等）

#### 4. 动作执行阶段

- **坐标补充**: 对需要坐标的动作，使用坐标模型精确定位
- **动作执行**: 通过ADB命令执行具体的UI操作
- **结果记录**: 记录执行结果和耗时信息

#### 5. 验证反馈阶段

- **执行后截图**: 拍摄动作执行后的界面状态
- **步骤验证**:
    - 分步模式：验证当前步骤是否达到预期结果
    - 聚合模式：记录执行历史，为下轮决策提供参考
- **状态更新**: 更新任务状态，决定是否继续执行或完成任务

## 🚀 快速开始

### 环境要求

- Python 3.11+
- MySQL 5.7+ (可选，用于测试数据存储)
- Poetry (依赖管理)
- Android设备或模拟器 (用于移动端测试)
- Docker (可选，用于容器化部署)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd click-pilot
   ```

2. **安装依赖**
   ```bash
   poetry install
   ```

3. **激活虚拟环境**
   ```bash
   poetry shell
   ```

4. **配置环境**
   ```bash
   # 复制配置模板
   cp config/example.toml config/development.toml
   # 编辑配置文件，填入测试设备信息、数据库连接等
   ```

5. **数据库迁移** (可选)
   ```bash
   alembic revision --autogenerate -m "Initial migration"
   alembic upgrade head
   ```

6. **启动服务**
   ```bash
   python main.py
   ```

### Docker 部署 (可选)

1. **构建镜像**
   ```bash
   docker build -t click-pilot .
   ```

2. **运行容器**
   ```bash
   docker run -d -p 8000:8000 \
     -v $(pwd)/config:/usr/app/config \
     -v $(pwd)/screenshots:/usr/app/screenshots \
     -v $(pwd)/reports:/usr/app/reports \
     click-pilot
   ```

## 📖 使用指南

### API 服务

启动后访问 `http://localhost:8000` 查看 API 文档

主要端点：

- `GET /` - 系统信息 (返回 "ClickPilot")
- `GET /version` - 版本信息
- `GET /docs` - Swagger API 文档
- `POST /api/v1/ui-task/create` - 创建UI自动化测试任务
- `GET /api/v1/ui-task/status/get` - 获取任务状态
- `POST /api/v1/ui-task/stop` - 停止正在运行的任务
- `POST /api/v1/ui-task/video/stream/upload` - 上传测试视频
- `POST /api/v1/ui-task/video/analyze` - AI视频分析

### 测试配置

1. **设备配置**: 在配置文件中设置目标测试设备信息
2. **应用配置**: 指定要测试的APP包名和启动Activity
3. **测试策略**: 配置决策Agent的测试策略和目标
4. **AI配置**: 配置豆包AI API密钥用于视频分析功能
5. **Prompt参数化**: 自定义应用描述、UI组件操作说明和特殊场景处理

### Agent 工作模式

#### 决策Agent工作流程

1. **界面深度分析**: 解析截图中的所有UI元素、文本和组件状态
2. **执行历史回顾**: 分析最近3-5次动作的执行效果，识别重复或无效操作
3. **自检机制**: 检测重复动作、验证动作有效性、分析界面状态变化
4. **策略制定**: 基于当前状态和测试目标，制定最优执行策略
5. **动作生成**: 输出结构化的动作命令和详细的思考过程

#### 执行Agent工作流程

1. **动作类型判断**: 识别动作是否需要坐标补充
2. **坐标模型调用**: 对需要坐标的动作，使用专门模型进行精确定位
3. **ADB命令执行**: 通过Android调试桥执行具体的UI操作
4. **结果反馈**: 记录执行状态、耗时和坐标响应信息
5. **截图管理**: 处理执行后截图的拍摄和存储

#### 监督Agent工作流程

1. **设备连接检查**: 验证Android设备连接状态和命令响应能力
2. **应用状态监控**: 检查目标应用安装状态和前台运行情况
3. **自动应用切换**: 当应用不在前台时，自动启动或切换到目标应用
4. **执行限制监控**: 基于步骤数量动态设置和扩展执行限制
5. **AI错误检测**: 使用大模型分析执行历史，识别死循环和执行错误

### 视频分析功能

系统支持AI驱动的视频分析功能：

- **视频上传**: 支持流式上传大文件视频（最大50MB）
- **AI分析**: 使用豆包AI分析视频内容，自动生成测试用例步骤
- **智能识别**: 识别用户操作行为，生成对应的测试步骤和期望结果
- **自动清理**: 定时清理过期视频文件，节省存储空间

### 任务调度

系统内置任务调度器，支持：

- 定时自动化测试
- 批量测试任务
- 测试报告生成
- 视频文件自动清理（每天凌晨2点执行）

### 飞书集成

支持飞书机器人消息推送，可用于：

- 测试结果通知
- 异常情况报警
- 测试进度更新

## 🔧 开发指南

## 📖 API 接口

### 核心任务接口

| 方法     | 端点                                      | 描述              |
|--------|-----------------------------------------|-----------------|
| POST   | `/api/v1/ui-task/create`                | 创建一个新的UI自动化任务   |
| GET    | `/api/v1/ui-task/status/get`            | 获取任务的状态         |
| GET    | `/api/v1/ui-task/record/get`            | 获取任务的执行记录       |
| GET    | `/api/v1/ui-task/log/get`               | 获取任务的日志         |
| POST   | `/api/v1/ui-task/stop`                  | 停止一个正在运行的任务     |
| DELETE | `/api/v1/ui-task/delete`                | 删除一个任务及其所有相关数据  |
| GET    | `/api/v1/ui-task/screenshot`            | 获取任务的截图         |

### 视频分析接口

| 方法   | 端点                                    | 描述              |
|------|---------------------------------------|-----------------|
| POST | `/api/v1/ui-task/video/stream/upload` | 流式上传视频文件        |
| POST | `/api/v1/ui-task/video/analyze`       | AI分析视频并生成测试用例   |
| GET  | `/api/v1/ui-task/video/stream/status` | 获取视频流式上传的状态     |
| GET  | `/api/v1/ui-task/video/stream/list`   | 列出所有活动的视频流式上传会话 |

### 新增功能特性

- **视频上传优化**: 支持最大50MB视频文件，自动生成UUID会话ID
- **按日期存储**: 视频文件按日期（YYYYMMDD）分类存储
- **AI智能分析**: 基于豆包AI的视频内容分析和测试用例自动生成
- **自动清理**: 定时清理2天前的视频文件，节省存储空间

### 代码规范

- 使用 Black 进行代码格式化
- 使用 isort 进行导入排序
- 遵循 PEP 8 编码规范
- 采用DDD (领域驱动设计) 架构

### 测试

```bash
# 运行单元测试
pytest

# 生成覆盖率报告
pytest --cov=src

# 运行UI自动化测试
python main.py --test-mode
```

## 📦 依赖包

### 核心框架

- **FastAPI**: Web API框架
- **SQLAlchemy**: ORM 框架 (可选)
- **APScheduler**: 任务调度器
- **Loguru**: 日志系统
- **Pydantic**: 数据验证
- **Requests**: HTTP客户端

### AI和自动化

- **OpenAI**: AI模型接口
- **LangChain**: AI应用开发框架
- **LangGraph**: 图形化AI工作流
- **ADBUtils**: Android设备调试工具

### 图像和视频处理

- **OpenCV-Python**: 计算机视觉库
- **Pillow**: 图像处理库
- **NumPy**: 数值计算库

### 其他工具

- **Lark-OAPI**: 飞书机器人SDK
- **TuShare**: 数据分析工具
- **Python-Multipart**: 多部分数据处理
- **Tenacity**: 重试机制库

## 🎯 应用场景

- 📱 **移动应用测试**: Android/iOS应用的自动化UI测试
- 🌐 **Web应用测试**: 浏览器端的自动化测试
- 🔄 **回归测试**: 自动化的功能回归验证
- 📊 **性能测试**: UI操作性能和响应时间测试
- 🛒 **电商测试**: 购物流程的自动化验证
- 🎬 **视频分析测试**: 基于用户操作视频的测试用例自动生成
- 🤖 **AI驱动测试**: 智能化的测试策略制定和执行优化
- 🔧 **定制化测试**: 支持不同应用的个性化测试配置

## 🆕 最新更新

### v2.0 新增功能

- ✨ **AI视频分析**: 集成豆包AI，支持视频内容分析和测试用例自动生成
- 📤 **视频流式上传**: 优化大文件上传体验，支持最大50MB视频文件
- ⌨️ **智能键盘管理**: 新增ADB键盘自动化管理，支持多种输入法切换
- 🖼️ **统一图像处理**: 整合图像标注、压缩、格式转换等功能到单一模块
- 🛡️ **任务停止管理**: 实现基于任务ID的精确停止控制机制
- 🧹 **自动清理机制**: 定时清理过期视频文件，优化存储管理
- 🔧 **Prompt参数化**: 支持自定义应用描述和UI操作说明
- 🐳 **Docker支持**: 新增容器化部署配置，简化部署流程


