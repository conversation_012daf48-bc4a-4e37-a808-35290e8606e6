from typing import Dict, Any


def get_action_verification_prompt(decision_fields: Dict[str, Any]) -> str:
    """
    获取动作执行验证提示
    
    Args:
        decision_fields: 决策字段
        
    Returns:
        验证提示文本
    """
    # 转义模板变量
    element_description = _escape_template_variables(decision_fields.get("element_description", ""))
    
    prompt = f"""
########## 角色 ##########
{get_action_verification_role()}

########## 元素描述 ##########
{element_description}

########## 准星标记 ##########
{get_crosshair_description()}

########## 验证方式 ##########
{get_action_verification_method()}

########### 验证规则 ##########
{get_action_verification_rules()}

########### 输出字段说明 ##########
{get_action_output_fields()}

########## 输出格式 ##########
{get_action_output_format()}

########## 输出要求 ##########
{get_action_output_requirements()}

"""
    return prompt


def get_action_verification_role() -> str:
    """获取动作验证角色定义"""
    return "你是一个UI自动化测试执行验证Agent，专门负责验证元素是否被准星正确标记"


def get_crosshair_description() -> str:
    """获取准星标记说明"""
    return """
1. 准星标记，用于标记动作操作的具体位置
2. 准星标记外层一个红圈，中心有一个红色实心圆点
"""


def get_action_verification_method() -> str:
    """获取动作验证方式"""
    return "1.首先对比<标记前截图>和<标记后截图>，找到<准星标记>所选中的元素，与<执行指令>描述的元素对比，判断位置是否一致"


def get_action_verification_rules() -> str:
    """获取动作验证规则"""
    return "1.着重于元素的位置、文字内容内容，忽略颜色差异"


def get_action_output_fields() -> str:
    """获取动作输出字段说明"""
    return """
verified: 是否验证通过，取值：True/False
reason: 验证失败时,给出失败原因
"""


def get_action_output_format() -> str:
    """获取动作输出格式"""
    return """
仅输出以下JSON格式，不要输出其他内容
{{
  "verified": "验证结果，取值true/false", 
  "reason": "描述错误元素；描述正确元素的位置以及特征(保持语言简洁)"
}}
"""


def get_action_output_requirements() -> str:
    """获取动作输出要求"""
    return "严格按照<输出格式>格式输出"


def _escape_template_variables(text: str) -> str:
    """
    转义文本中的模板变量符号，防止LangChain将其识别为变量
    
    Args:
        text: 原始文本
        
    Returns:
        转义后的文本
    """
    if not text:
        return text
    
    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")