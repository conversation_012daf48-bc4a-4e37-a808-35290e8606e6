#!/usr/bin/env python3
"""
ADB键盘管理器

负责管理Android设备上的ADB键盘启用、禁用和设置
"""

import subprocess
import time
from typing import List, Dict, Optional, Tuple
from loguru import logger


class KeyboardManager:
    """ADB键盘管理器"""

    # 支持的键盘配置
    KEYBOARDS = [
        {
            "name": "AdbIME",
            "package": "com.android.adbkeyboard/.AdbIME",
            "priority": 2
        },
        {
            "name": "FastInputIME",
            "package": "com.github.uiautomator/.FastInputIME",
            "priority": 1
        }
    ]

    def __init__(self, device_id: str):
        """
        初始化键盘管理器
        
        Args:
            device_id: 设备ID
        """
        self.device_id = device_id
        self.enabled_keyboards: List[str] = []
        self.original_ime: Optional[str] = None

    def _execute_adb_command(self, command: str, timeout: int = 10) -> <PERSON><PERSON>[bool, str]:
        """
        执行ADB命令
        
        Args:
            command: ADB命令
            timeout: 超时时间（秒）
            
        Returns:
            Tuple[success, output]: 执行结果和输出
        """
        try:
            full_command = f"adb -s {self.device_id} {command}"
            logger.debug(f"[device: {self.device_id}] Executing: {full_command}")

            result = subprocess.run(
                full_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )

            success = result.returncode == 0
            output = result.stdout.strip() if success else result.stderr.strip()

            if success:
                logger.debug(f"[device: {self.device_id}] Command success: {output}")
            else:
                logger.warning(f"[device: {self.device_id}] Command failed: {output}")

            return success, output

        except subprocess.TimeoutExpired:
            logger.error(f"[device: {self.device_id}] Command timeout: {command}")
            return False, "Command timeout"
        except Exception as e:
            logger.error(f"[device: {self.device_id}] Command error: {str(e)}")
            return False, str(e)

    def get_current_ime(self) -> Optional[str]:
        """
        获取当前输入法
        
        Returns:
            当前输入法包名，失败返回None
        """
        success, output = self._execute_adb_command("shell settings get secure default_input_method")
        if success and output:
            return output
        return None

    def get_enabled_imes(self) -> List[str]:
        """
        获取已启用的输入法列表

        Returns:
            已启用的输入法包名列表
        """
        success, output = self._execute_adb_command("shell ime list -s")
        if success and output:
            # 解析输出，提取包名
            imes = []
            for line in output.split('\n'):
                line = line.strip()
                if line:
                    # 直接使用每一行作为输入法包名
                    # 输出格式就是简单的包名列表，每行一个
                    imes.append(line)
                    logger.debug(f"[device: {self.device_id}] Found enabled IME: {line}")
            logger.info(f"[device: {self.device_id}] Total enabled IMEs found: {len(imes)}")
            return imes
        return []

    def enable_keyboard(self, keyboard_package: str) -> bool:
        """
        启用指定键盘
        
        Args:
            keyboard_package: 键盘包名
            
        Returns:
            是否成功启用
        """
        success, _ = self._execute_adb_command(f"shell ime enable {keyboard_package}")
        if success:
            logger.info(f"[device: {self.device_id}] ✅ Enabled keyboard: {keyboard_package}")
        else:
            logger.warning(f"[device: {self.device_id}] ❌ Failed to enable keyboard: {keyboard_package}")
        return success

    def disable_keyboard(self, keyboard_package: str) -> bool:
        """
        禁用指定键盘
        
        Args:
            keyboard_package: 键盘包名
            
        Returns:
            是否成功禁用
        """
        success, _ = self._execute_adb_command(f"shell ime disable {keyboard_package}")
        if success:
            logger.info(f"[device: {self.device_id}] ✅ Disabled keyboard: {keyboard_package}")
        else:
            logger.warning(f"[device: {self.device_id}] ❌ Failed to disable keyboard: {keyboard_package}")
        return success

    def set_keyboard(self, keyboard_package: str) -> bool:
        """
        设置当前输入法
        
        Args:
            keyboard_package: 键盘包名
            
        Returns:
            是否成功设置
        """
        success, _ = self._execute_adb_command(f"shell ime set {keyboard_package}")
        if success:
            logger.info(f"[device: {self.device_id}] ✅ Set keyboard: {keyboard_package}")
        else:
            logger.warning(f"[device: {self.device_id}] ❌ Failed to set keyboard: {keyboard_package}")
        return success

    def setup_adb_keyboards(self) -> bool:
        """
        设置ADB键盘
        
        执行流程：
        1. 保存当前输入法
        2. 启用所有ADB键盘
        3. 检查哪些键盘启用成功
        4. 设置优先级最高的键盘
        
        Returns:
            是否成功设置了至少一个ADB键盘
        """
        try:
            logger.info(f"[device: {self.device_id}] 🎹 Setting up ADB keyboards...")

            # 1. 保存当前输入法
            self.original_ime = self.get_current_ime()
            logger.info(f"[device: {self.device_id}] Original IME: {self.original_ime}")

            # 2. 启用所有ADB键盘
            for keyboard in self.KEYBOARDS:
                package = keyboard["package"]
                name = keyboard["name"]
                logger.info(f"[device: {self.device_id}] Enabling {name}...")
                self.enable_keyboard(package)
                time.sleep(0.5)  # 短暂等待

            # 3. 检查哪些键盘启用成功
            time.sleep(1)  # 等待系统更新
            enabled_imes = self.get_enabled_imes()
            logger.info(f"[device: {self.device_id}] Enabled IMEs: {enabled_imes}")

            available_keyboards = []
            for keyboard in self.KEYBOARDS:
                if keyboard["package"] in enabled_imes:
                    available_keyboards.append(keyboard)
                    self.enabled_keyboards.append(keyboard["package"])
                    logger.info(f"[device: {self.device_id}] ✅ {keyboard['name']} is available")
                else:
                    logger.warning(f"[device: {self.device_id}] ❌ {keyboard['name']} is not available")

            # 4. 设置优先级最高的键盘
            if available_keyboards:
                # 按优先级排序
                available_keyboards.sort(key=lambda x: x["priority"])
                best_keyboard = available_keyboards[0]

                logger.info(f"[device: {self.device_id}] Setting best keyboard: {best_keyboard['name']}")
                success = self.set_keyboard(best_keyboard["package"])

                if success:
                    # 验证键盘是否真的被设置成功
                    time.sleep(1)  # 等待系统更新
                    current_ime = self.get_current_ime()
                    if current_ime == best_keyboard["package"]:
                        logger.info(
                            f"[device: {self.device_id}] 🎉 Successfully set up ADB keyboard: {best_keyboard['name']}")
                        return True
                    else:
                        logger.warning(
                            f"[device: {self.device_id}] ⚠️ Keyboard set command succeeded but current IME is still: {current_ime}")
                        return False
                else:
                    logger.error(f"[device: {self.device_id}] ❌ Failed to set keyboard: {best_keyboard['name']}")
            else:
                logger.warning(f"[device: {self.device_id}] ⚠️ No ADB keyboards available")

            return False

        except Exception as e:
            logger.error(f"[device: {self.device_id}] ❌ Error setting up ADB keyboards: {str(e)}")
            return False

    def cleanup_adb_keyboards(self) -> None:
        """
        清理ADB键盘设置
        
        执行流程：
        1. 恢复原始输入法（如果有）
        2. 禁用所有启用的ADB键盘
        """
        try:
            logger.info(f"[device: {self.device_id}] 🧹 Cleaning up ADB keyboards...")

            # 1. 恢复原始输入法
            if self.original_ime:
                logger.info(f"[device: {self.device_id}] Restoring original IME: {self.original_ime}")
                self.set_keyboard(self.original_ime)
                time.sleep(0.5)

            # 2. 禁用所有启用的ADB键盘
            for keyboard_package in self.enabled_keyboards:
                logger.info(f"[device: {self.device_id}] Disabling: {keyboard_package}")
                self.disable_keyboard(keyboard_package)
                time.sleep(0.5)

            logger.info(f"[device: {self.device_id}] ✅ ADB keyboards cleanup completed")

        except Exception as e:
            logger.error(f"[device: {self.device_id}] ❌ Error cleaning up ADB keyboards: {str(e)}")


# 全局键盘管理器注册表
keyboard_managers: Dict[str, KeyboardManager] = {}

# 已清理的设备记录（避免重复清理）
cleaned_devices: set = set()


def get_keyboard_manager(device_id: str) -> KeyboardManager:
    """
    获取设备的键盘管理器

    Args:
        device_id: 设备ID

    Returns:
        键盘管理器实例
    """
    if device_id not in keyboard_managers:
        keyboard_managers[device_id] = KeyboardManager(device_id)
        # 清除已清理标记（因为要重新设置键盘）
        cleaned_devices.discard(device_id)
    return keyboard_managers[device_id]


def cleanup_keyboard_manager(device_id: str) -> None:
    """
    清理设备的键盘管理器

    Args:
        device_id: 设备ID
    """
    # 检查是否已经清理过
    if device_id in cleaned_devices:
        logger.info(f"[device: {device_id}] Keyboard already cleaned up, skipping")
        return

    if device_id in keyboard_managers:
        keyboard_managers[device_id].cleanup_adb_keyboards()
        del keyboard_managers[device_id]
        # 标记为已清理
        cleaned_devices.add(device_id)
        logger.info(f"[device: {device_id}] Keyboard manager cleaned up")
    else:
        logger.warning(f"[device: {device_id}] No keyboard manager found for cleanup")
